{"name": "recipe_app", "short_name": "recipe_app", "start_url": ".", "display": "standalone", "background_color": "#0175C2", "theme_color": "#0175C2", "description": "A recipe app that uses Flutter and Go for backend operations to fetch recipe information from an API and displays it in a beuatiful UI.", "orientation": "portrait-primary", "prefer_related_applications": false, "icons": [{"src": "icons/Icon-192.png", "sizes": "192x192", "type": "image/png"}, {"src": "icons/Icon-512.png", "sizes": "512x512", "type": "image/png"}, {"src": "icons/Icon-maskable-192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "icons/Icon-maskable-512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}]}